// Diaporama JavaScript - Gestion complète du diaporama fullscreen
class Slideshow {
    constructor() {
        // Éléments DOM
        this.container = document.querySelector('.slideshow-container');
        this.slides = document.querySelectorAll('.slide');
        this.indicators = document.querySelectorAll('.indicator');
        this.prevBtn = document.querySelector('.prev-btn');
        this.nextBtn = document.querySelector('.next-btn');
        this.playPauseBtn = document.querySelector('.play-pause-btn');
        this.fullscreenBtn = document.querySelector('.fullscreen-btn');
        this.currentSlideSpan = document.querySelector('.current-slide');
        this.totalSlidesSpan = document.querySelector('.total-slides');

        // Variables d'état
        this.currentSlide = 0;
        this.totalSlides = this.slides.length;
        this.isPlaying = false;
        this.autoPlayInterval = null;
        this.autoPlayDelay = 4000; // 4 secondes

        // Variables pour le swipe mobile
        this.startX = 0;
        this.endX = 0;
        this.minSwipeDistance = 50;

        this.init();
    }

    init() {
        // Initialisation des événements
        this.setupEventListeners();
        
        // Mise à jour de l'affichage initial
        this.updateSlideCounter();
        
        // Préchargement des images
        this.preloadImages();
        
        console.log('Diaporama initialisé avec', this.totalSlides, 'images');
    }

    setupEventListeners() {
        // Boutons de navigation
        this.prevBtn.addEventListener('click', () => this.previousSlide());
        this.nextBtn.addEventListener('click', () => this.nextSlide());

        // Bouton play/pause
        this.playPauseBtn.addEventListener('click', () => this.toggleAutoPlay());

        // Bouton plein écran
        this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());

        // Indicateurs
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => this.goToSlide(index));
        });

        // Événements clavier
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Événements tactiles pour mobile
        this.container.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.container.addEventListener('touchend', (e) => this.handleTouchEnd(e));

        // Gestion du redimensionnement
        window.addEventListener('resize', () => this.handleResize());
    }

    // Navigation vers la slide suivante
    nextSlide() {
        this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
        this.updateSlide();
    }

    // Navigation vers la slide précédente
    previousSlide() {
        this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.updateSlide();
    }

    // Aller à une slide spécifique
    goToSlide(index) {
        this.currentSlide = index;
        this.updateSlide();
    }

    // Mise à jour de l'affichage de la slide
    updateSlide() {
        // Masquer toutes les slides
        this.slides.forEach(slide => slide.classList.remove('active'));
        
        // Afficher la slide courante
        this.slides[this.currentSlide].classList.add('active');

        // Mettre à jour les indicateurs
        this.indicators.forEach(indicator => indicator.classList.remove('active'));
        this.indicators[this.currentSlide].classList.add('active');

        // Mettre à jour le compteur
        this.updateSlideCounter();
    }

    // Mise à jour du compteur de slides
    updateSlideCounter() {
        this.currentSlideSpan.textContent = this.currentSlide + 1;
        this.totalSlidesSpan.textContent = this.totalSlides;
    }

    // Gestion de l'auto-play
    toggleAutoPlay() {
        if (this.isPlaying) {
            this.stopAutoPlay();
        } else {
            this.startAutoPlay();
        }
    }

    startAutoPlay() {
        this.isPlaying = true;
        this.playPauseBtn.classList.add('playing');
        this.autoPlayInterval = setInterval(() => {
            this.nextSlide();
        }, this.autoPlayDelay);
    }

    stopAutoPlay() {
        this.isPlaying = false;
        this.playPauseBtn.classList.remove('playing');
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }

    // Gestion du plein écran
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            this.container.requestFullscreen().catch(err => {
                console.log('Erreur plein écran:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    // Gestion des événements clavier
    handleKeyboard(e) {
        switch(e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.previousSlide();
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.nextSlide();
                break;
            case ' ':
                e.preventDefault();
                this.toggleAutoPlay();
                break;
            case 'Escape':
                this.stopAutoPlay();
                break;
            case 'f':
            case 'F':
                this.toggleFullscreen();
                break;
        }
    }

    // Gestion des événements tactiles
    handleTouchStart(e) {
        this.startX = e.touches[0].clientX;
    }

    handleTouchEnd(e) {
        this.endX = e.changedTouches[0].clientX;
        this.handleSwipe();
    }

    handleSwipe() {
        const distance = this.startX - this.endX;
        
        if (Math.abs(distance) > this.minSwipeDistance) {
            if (distance > 0) {
                // Swipe vers la gauche - slide suivante
                this.nextSlide();
            } else {
                // Swipe vers la droite - slide précédente
                this.previousSlide();
            }
        }
    }

    // Gestion du redimensionnement
    handleResize() {
        // Réajustement si nécessaire lors du changement d'orientation
        console.log('Redimensionnement détecté');
    }

    // Préchargement des images pour une meilleure performance
    preloadImages() {
        this.slides.forEach((slide, index) => {
            const img = slide.querySelector('img');
            if (img && !img.complete) {
                img.addEventListener('load', () => {
                    console.log(`Image ${index + 1} chargée`);
                });
            }
        });
    }
}

// Initialisation du diaporama quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    const slideshow = new Slideshow();
    
    // Exposition globale pour le débogage
    window.slideshow = slideshow;
});
