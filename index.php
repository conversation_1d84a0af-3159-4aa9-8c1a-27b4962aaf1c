<!DOCTYPE html>
<html lang="ja">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>馬場先木工所 - Babasaki Woodworking Factory</title>
    <meta name="description" content="職人のこだわり―茨城県ひたちなか市の建具専門店">
    <meta name="keywords" content="こだわり,職人,技,達人,歴史,茨城,ひたちなか,家具,建具,門戸,組子,オーダーメイド,特注,修理">
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="diaporama.css">
</head>

<body class="bg-white text-brown">
    <!-- Header -->
    <header class="bg-light-beige">
        <div class="header-wrapper">
            <div class="header-container">
                <!-- Left Side: Logo and Contact Icons (Fixed Position) -->
                <div class="left-side">
                    <!-- Logo -->
                    <div id="logo" class="z-10 transition-all duration-500">
                        <a href="index.html" class="block logo-link">
                            <div class="logo">
                                <p class="logo-en">BABASAKI's woodworking factory</p>
                                <p class="logo-jp">馬場先木工所</p>
                            </div>
                        </a>
                    </div>

                    <!-- Contact Icons (Mobile and Tablet) -->
                    <div class="contact-icons-mobile">
                        <button id="phone-icon" class="contact-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-brown" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                        </button>
                        <button id="fax-icon" class="contact-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-brown" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                            </svg>
                        </button>
                    </div>

                    <!-- Contact Info (Large Desktop) -->
                    <div class="contact-info-desktop">
                        <div>
                            <div class="contact-item">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-brown mr-1" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <span class="text-sm font-bold">TEL: <span class="text-lg">************</span></span>
                            </div>
                            <div class="contact-item">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-brown mr-1" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                                </svg>
                                <span class="text-sm font-bold">FAX: <span class="text-lg">************</span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Navigation (Fixed Position) -->
                <div class="right-side">
                    <!-- Hamburger Menu Button (Mobile) -->
                    <div class="burger-container">
                        <div id="menu-toggle" class="hamburger-menu">
                            <div class="icon-left"></div>
                            <div class="icon-right"></div>
                        </div>
                    </div>

                    <!-- Desktop Navigation -->
                    <nav class="desktop-nav" aria-label="Menu principal">
                        <a href="#about" class="nav-link">馬場先木工所とは</a>
                        <a href="performance.html" class="nav-link">制作実績・賞歴</a>
                        <a href="inquiry.html" class="nav-link">お問い合わせ</a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Hidden Contact Numbers for Mobile Animation -->
        <div id="contact-numbers" class="contact-numbers-container">
            <div class="contact-numbers-inner">
                <div class="contact-row">
                    <button id="phone-icon-contact" class="contact-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-brown mr-2" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                    </button>
                    <span class="contact-text">TEL: <span class="contact-number">************</span></span>
                </div>
                <div class="contact-row">
                    <button id="fax-icon-contact" class="contact-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-brown mr-2" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                        </svg>
                    </button>
                    <span class="contact-text">FAX: <span class="contact-number">************</span></span>
                </div>
            </div>
        </div>

    </header>

    <!-- Mobile Menu (déplacé en dehors du header) -->
    <div id="mobile-menu">
        <div class="mobile-menu-content">
            <nav class="mobile-nav" aria-label="Menu mobile">
                <a href="#about" class="mobile-nav-link">馬場先木工所とは</a>
                <a href="performance.html" class="mobile-nav-link">制作実績・賞歴</a>
                <a href="inquiry.html" class="mobile-nav-link">お問い合わせ</a>
            </nav>
        </div>
        <!-- Background image will be added via CSS -->
        <div class="mobile-menu-bg"></div>
    </div>

    <!-- Main Content -->
    <main>
        <?php
        // Scan automatique du dossier diaporama pour récupérer toutes les images
        $diaporama_folder = 'diaporama/';
        $diaporama_images = [];

        // Extensions d'images supportées
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];

        // Vérifier si le dossier existe
        if (is_dir($diaporama_folder)) {
            // Scanner le dossier
            $files = scandir($diaporama_folder);

            foreach ($files as $file) {
                // Ignorer les dossiers . et ..
                if ($file != '.' && $file != '..') {
                    // Récupérer l'extension du fichier
                    $file_extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));

                    // Vérifier si c'est une image supportée
                    if (in_array($file_extension, $allowed_extensions)) {
                        $diaporama_images[] = $diaporama_folder . $file;
                    }
                }
            }

            // Trier les images par nom pour un ordre cohérent
            sort($diaporama_images);
        }

        // Nombre total d'images trouvées
        $total_images = count($diaporama_images);
        ?>

        <?php if ($total_images > 0): ?>
        <!-- Diaporama généré automatiquement avec <?php echo $total_images; ?> images -->

        <!-- Container principal du diaporama -->
        <div class="slideshow-container fullscreen">
            <?php foreach ($diaporama_images as $index => $image): ?>
            <!-- Slide <?php echo $index + 1; ?> -->
            <div class="slide <?php echo $index === 0 ? 'active' : ''; ?>">
                <img src="<?php echo $image; ?>" alt="Diaporama <?php echo $index + 1; ?>" loading="lazy">
            </div>
            <?php endforeach; ?>

            <!-- Texte central japonais vertical -->
            <h1 class="central-text">
                <div class="japanese-vertical-text">
                    <span>職人の手が生きる、</span><br>
                    <span>確かな仕上がり</span>
                </div>
            </h1>

            <!-- Contrôles de navigation -->
            <button class="nav-btn prev-btn" aria-label="Image précédente">
                <span>&#8249;</span>
            </button>
            <button class="nav-btn next-btn" aria-label="Image suivante">
                <span>&#8250;</span>
            </button>

            <!-- Contrôles de lecture -->
            <div class="controls">
                <button class="control-btn play-pause-btn" aria-label="Lecture/Pause">
                    <span class="play-icon">&#9654;</span>
                    <span class="pause-icon">&#10074;&#10074;</span>
                </button>
                <button class="control-btn fullscreen-btn" aria-label="Plein écran">
                    <span>&#9974;</span>
                </button>
            </div>

            <!-- Indicateurs -->
            <div class="indicators">
                <?php foreach ($diaporama_images as $index => $image): ?>
                <button class="indicator <?php echo $index === 0 ? 'active' : ''; ?>"
                    data-slide="<?php echo $index; ?>"></button>
                <?php endforeach; ?>
            </div>

            <!-- Compteur de slides -->
            <div class="slide-counter">
                <span class="current-slide">1</span> / <span class="total-slides"><?php echo $total_images; ?></span>
            </div>
        </div>

        <?php else: ?>
        <!-- Message d'erreur si aucune image trouvée -->
        <div class="no-images-message"
            style="display: flex; align-items: center; justify-content: center; height: 100vh; background: #000; color: white; text-align: center;">
            <div>
                <h2 style="font-size: 2rem; margin-bottom: 1rem;">Aucune image trouvée</h2>
                <p style="font-size: 1.2rem;">Veuillez ajouter des images dans le dossier "diaporama/"</p>
                <p style="font-size: 1rem; opacity: 0.7; margin-top: 1rem;">
                    Extensions supportées : JPG, JPEG, PNG, GIF, WEBP, BMP
                </p>
            </div>
        </div>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-brown text-white relative pb-10 mt-auto">
        <div class="container mx-auto px-4 pt-6 text-center">
            <div class="mb-8">
                <p class="text-lg font-bold mb-0.5">馬場先木工所</p>
                <p class="mb-0.5">茨城県 ひたちなか市 殿山町2丁目1-6</p>
                <p>TEL: ************</p>
            </div>
            <div class="absolute bottom-2 left-0 right-0">
                <p class="text-xs">&copy; 2023 馬場先木工所. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="diaporama.js"></script>
</body>

</html>