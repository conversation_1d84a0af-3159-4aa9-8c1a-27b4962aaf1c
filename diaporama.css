/* Reset et base styles pour le diaporama */
.slideshow-container * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Container principal - Adaptatif selon l'état du header */
.slideshow-container {
    position: relative;
    width: 100vw;
    background: #000;
    overflow: hidden;
    transition: height 0.5s ease, top 0.5s ease;
}

/* <PERSON><PERSON> fullscreen (desktop uniquement, header caché) */
.slideshow-container.fullscreen {
    height: 100vh;
    top: 0;
    position: fixed;
    z-index: 50;
}

/* État normal (header visible) - Position relative pour permettre au footer d'être visible */
.slideshow-container.with-header {
    height: calc(100vh - 80px);
    position: relative;
    margin-top: 80px; /* Compenser la hauteur du header fixe */
    z-index: 10;
}

/* Mobile - toujours avec header et position relative */
@media (max-width: 768px) {
    .slideshow-container {
        height: calc(100vh - 80px) !important;
        position: relative !important;
        margin-top: 80px !important;
        z-index: 10 !important;
        /* Éliminer toute marge ou padding sur mobile */
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding: 0 !important;
        border: none !important;
        outline: none !important;
        /* S'assurer que le container couvre toute la largeur */
        left: 0 !important;
        right: 0 !important;
        width: 100vw !important;
    }

    .slideshow-container.fullscreen {
        height: calc(100vh - 80px) !important;
        position: relative !important;
        margin-top: 80px !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding: 0 !important;
        width: 100vw !important;
    }
}

/* Slides */
.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    z-index: 1;
    /* Éliminer toute marge ou padding */
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    overflow: hidden; /* Empêcher tout débordement */
}

.slide.active {
    opacity: 1;
    z-index: 2;
}

/* Renforcement pour mobile - Slides parfaitement ajustées */
@media (max-width: 768px) {
    .slide {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        outline: none !important;
        overflow: hidden !important;
        /* S'assurer que le slide couvre exactement l'espace */
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
    }
}

/* Images - Contraintes pour même taille et suppression bandes blanches */
.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Recadre l'image pour remplir le container sans déformation */
    object-position: center; /* Centre l'image */
    display: block;
    /* Forcer le remplissage complet même sur mobile */
    min-width: 100%;
    min-height: 100%;
}

/* Renforcement pour mobile - Assurer que les images remplissent toujours l'écran */
@media (max-width: 768px) {
    .slide img {
        object-fit: cover !important; /* Force le recadrage même sur mobile */
        width: 100% !important;
        height: 100% !important;
        min-width: 100% !important;
        min-height: 100% !important;
        /* Éliminer toute marge ou padding potentiel */
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        outline: none !important;
    }
}

/* Renforcement pour très petits écrans */
@media (max-width: 480px) {
    .slide img {
        object-fit: cover !important;
        object-position: center !important;
        /* S'assurer que l'image couvre vraiment tout l'espace */
        transform: scale(1.01); /* Légère augmentation pour éliminer les bordures potentielles */
    }
}

/* Boutons de navigation */
.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 15px 20px;
    font-size: 24px;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    border-radius: 5px;
}

.nav-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-50%) scale(1.1);
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

/* Contrôles de lecture */
.controls {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
    display: flex;
    gap: 10px;
}

.control-btn {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px 15px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Icônes play/pause */
.play-pause-btn .pause-icon {
    display: none;
}

.play-pause-btn.playing .play-icon {
    display: none;
}

.play-pause-btn.playing .pause-icon {
    display: inline;
}

/* Indicateurs */
.indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.5);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active,
.indicator:hover {
    background: white;
    border-color: white;
}

/* Compteur de slides */
.slide-counter {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 10;
}

/* Responsive - Mobile First */
@media (max-width: 768px) {
    .nav-btn {
        padding: 12px 15px;
        font-size: 20px;
    }

    .prev-btn {
        left: 10px;
    }

    .next-btn {
        right: 10px;
    }

    .controls {
        top: 10px;
        right: 10px;
        gap: 8px;
    }

    .control-btn {
        padding: 8px 12px;
        font-size: 14px;
    }

    .indicators {
        bottom: 20px;
        gap: 8px;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }

    .slide-counter {
        bottom: 10px;
        right: 10px;
        padding: 6px 10px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .nav-btn {
        padding: 10px 12px;
        font-size: 18px;
    }

    .prev-btn {
        left: 5px;
    }

    .next-btn {
        right: 5px;
    }

    .controls {
        top: 5px;
        right: 5px;
    }

    .indicators {
        bottom: 15px;
        gap: 6px;
    }

    .slide-counter {
        bottom: 5px;
        right: 5px;
    }
}

/* Animation de fondu pour les transitions */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Styles pour le mode plein écran */
.slideshow-container:fullscreen {
    background: #000;
}

.slideshow-container:-webkit-full-screen {
    background: #000;
}

.slideshow-container:-moz-full-screen {
    background: #000;
}

.slideshow-container:-ms-fullscreen {
    background: #000;
}
